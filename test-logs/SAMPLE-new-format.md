# Integration Test Report (NEW FORMAT)
Generated: 2025-07-07T16:00:00.000Z
Session ID: sample-demo-session

## Summary
- Total Tests: 2
- Successful: 2
- Failed: 0
- Success Rate: 100.0%
- Total Duration: 15.12s
- Average Duration: 7.56s

## Test Results

### prompt_1 ✅
- **Prompt**: "there's this one senior person on my team"
- **Duration**: 4.23s
- **Timestamp**: 2025-07-07T16:00:00.000Z
- **Conversation ID**: 294
- **Message Count**: 3
- **Theme**: workplace dynamics
- **Skills**: professionalism, awareness

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 3
- **Characters Responding**: <PERSON><PERSON>, <PERSON>, <PERSON>
- **Total Duration**: 8.0s

**Message Timeline:**
1. **Fora** (after 2.0s): "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄"
2. **Jan** (after 5.0s): "Okay, so there's a senior person on your team. What's the vibe with them?"
3. **Lou** (after 8.0s): "Oh, dealing with senior folks can be a whole vibe. What's up with them?"

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 3 delayed messages
- **Average Planned Delay**: 2667ms
- **Processing Time**: 4.23s
- **Character Decisions**: Fora (1), Jan (1), Lou (1)

<details>
<summary>🔍 Detailed Technical Breakdown (Click to expand)</summary>

**All Server Responses and Queue Operations:**
1. **Fora** [Server Response, 2000ms delay]: "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄"
2. **Jan** [Server Response, 3000ms delay]: "Okay, so there's a senior person on your team. What's the vibe with them?"
3. **Lou** [Server Response, 3000ms delay]: "Oh, dealing with senior folks can be a whole vibe. What's up with them?"
4. **Fora** [Queued Message, 2000ms delay]: "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄"
5. **Jan** [Queued Message, 5000ms delay]: "Okay, so there's a senior person on your team. What's the vibe with them?"
6. **Lou** [Queued Message, 8000ms delay]: "Oh, dealing with senior folks can be a whole vibe. What's up with them?"

</details>

### prompt_2 ✅
- **Prompt**: "and they always talk to me like i'm a child"
- **Duration**: 6.88s
- **Timestamp**: 2025-07-07T16:00:05.000Z
- **Conversation ID**: 294
- **Message Count**: 6
- **Theme**: conflict resolution
- **Skills**: professionalism, awareness, Conflict Resolution, Emotional Intelligence, Workplace Etiquette

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 6
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 11.0s

**Message Timeline:**
1. **Fora** (after 2.0s): "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄"
2. **Fora** (after 3.0s): "Ugh, that's rough. Dealing with people who talk down to you is never the vibe. 🙄 It sounds like a tricky situation..."
3. **Jan** (after 5.0s): "Okay, so there's a senior person on your team. What's the vibe with them?"
4. **Jan** (after 7.0s): "Ugh, that's rough. Being talked down to is the worst. Have you tried addressing it directly, but calmly?"
5. **Lou** (after 8.0s): "Oh, dealing with senior folks can be a whole vibe. What's up with them?"
6. **Lou** (after 11.0s): "Oh, that's super frustrating. Being talked down to is never okay, especially when you're trying to do your job."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 6 delayed messages
- **Average Planned Delay**: 3667ms
- **Processing Time**: 6.88s
- **Character Decisions**: Fora (2), Jan (2), Lou (2)

---

## Key Improvements in This Format:

### 🎭 User Experience Focus
- **Clear Timeline**: Shows exactly what messages appear and when
- **Realistic Simulation**: Demonstrates the actual chat experience
- **No Confusion**: Eliminates duplicate-looking entries

### 🔧 Technical Clarity
- **Separated Concerns**: User experience vs. debugging information
- **Detailed Breakdown**: Available but collapsed by default
- **Better Labels**: Clear distinction between server responses and queued messages

### 📊 Better Analysis
- **Meaningful Metrics**: Focus on user-facing timing and flow
- **Character Behavior**: Clear view of how characters respond over time
- **Performance Insights**: Processing time vs. user experience duration
