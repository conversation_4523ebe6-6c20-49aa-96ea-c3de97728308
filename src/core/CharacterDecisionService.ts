import { DBOS } from '@dbos-inc/dbos-sdk';
import { PromptService } from './PromptService';
import { ConversationService } from './ConversationService';
import { MoodService } from './MoodService';
import { LLMService } from '../services/LLMService';
import { GeminiLLMService } from '../services/GeminiLLMService';
import { MessageQueueService } from './MessageQueueService';
import { logger } from '../utils/Logger';
import { ThemeAnalysisResult } from './ThemeAnalysisService';

export interface CharacterDecisionInput {
  character: string;
  userMessage: string;
  conversationId: number;
  themeAnalysis: ThemeAnalysisResult;
  previousCharacterMessages?: Array<{character: string, text: string, delay: number}>;
  isFollowUp?: boolean;
}

export interface CharacterDecisionResult {
  shouldRespond: boolean;
  response?: {
    text: string;
    delay: number;
  };
  reasoning?: string;
}

export class CharacterDecisionService {
  private static llmService: LLMService | null = null;

  // Method to set LLM service for testing
  static setLLMService(service: LLMService): void {
    CharacterDecisionService.llmService = service;
  }

  // Helper method to get the LLM service (injected or default)
  private static getLLMService(): LLMService {
    return CharacterDecisionService.llmService || new GeminiLLMService();
  }

  /**
   * Have a character independently decide whether to respond to user input
   */
  @DBOS.workflow()
  static async getCharacterDecision(input: CharacterDecisionInput): Promise<CharacterDecisionResult> {
    const { character, userMessage, conversationId, themeAnalysis, previousCharacterMessages, isFollowUp } = input;
    
    // Get character-specific system prompt with decision-making capability
    const characterPromptName = `${character.toLowerCase()}_decision_system`;
    let systemPrompt = await PromptService.getSystemPrompt(characterPromptName);
    
    // Add mood context to the system prompt
    const conversation = await ConversationService.getConversation(conversationId);
    if (conversation?.character_moods) {
      const moods = MoodService.parseCharacterMoods(conversation.character_moods);
      if (moods) {
        const moodContext = MoodService.formatCharacterMoodForPrompt(character, moods);
        if (moodContext) {
          systemPrompt = `${systemPrompt}\n\n${moodContext}`;
        }
      }
    }

    // Add specialist context to the system prompt for Fora
    if (character.toLowerCase() === 'fora' && themeAnalysis.specialists.length > 0) {
      const specialistContext = CharacterDecisionService.formatSpecialistContext(themeAnalysis.specialists);
      systemPrompt = `${systemPrompt}\n\n${specialistContext}`;
    }
    
    // Build decision prompt with context
    const decisionPrompt = CharacterDecisionService.buildDecisionPrompt(input);

    // Generate character decision
    const decisionData = await CharacterDecisionService.getLLMService().generateJSON(systemPrompt, decisionPrompt);

    // Log the decision
    logger.info(`=== CHARACTER DECISION: ${character} ===`);
    logger.info(`Conversation: ${conversationId}, Theme: ${themeAnalysis.theme}`);
    logger.info(`Decision: ${JSON.stringify(decisionData)}`);

    // Validate the response structure
    if (!decisionData || typeof decisionData !== 'object') {
      return { shouldRespond: false, reasoning: 'No valid response from LLM' };
    }

    const result: CharacterDecisionResult = {
      shouldRespond: decisionData.shouldRespond === true,
      reasoning: decisionData.reasoning || 'No reasoning provided'
    };

    if (result.shouldRespond && decisionData.response) {
      result.response = {
        text: decisionData.response.text || '',
        delay: decisionData.response.delay || CharacterDecisionService.calculateDefaultDelay(themeAnalysis, isFollowUp)
      };
    }
    
    return result;
  }
  
  /**
   * Process all characters' decisions and queue appropriate responses
   */
  @DBOS.workflow()
  static async processAllCharacterDecisions(
    userMessage: string,
    conversationId: number,
    themeAnalysis: ThemeAnalysisResult
  ): Promise<Array<{character: string, queued: boolean, reasoning: string, response?: {text: string, delay: number}}>> {
    
    if (!themeAnalysis.shouldEngageCharacters) {
      logger.info(`Skipping character decisions - theme '${themeAnalysis.theme}' doesn't require character engagement`);
      return [];
    }
    
    const characters = ['Fora', 'Jan', 'Lou'];
    const results: Array<{character: string, queued: boolean, reasoning: string, response?: {text: string, delay: number}}> = [];
    const characterResponses: Array<{character: string, text: string, delay: number}> = [];
    
    // Get initial decisions from all characters
    for (const character of characters) {
      const decision = await CharacterDecisionService.getCharacterDecision({
        character,
        userMessage,
        conversationId,
        themeAnalysis,
        isFollowUp: false
      });
      
      if (decision.shouldRespond && decision.response) {
        characterResponses.push({
          character,
          text: decision.response.text,
          delay: decision.response.delay
        });
      }
      
      results.push({
        character,
        queued: decision.shouldRespond,
        reasoning: decision.reasoning || 'No reasoning provided',
        response: decision.response
      });
    }
    
    // Sort responses by delay (shortest first)
    characterResponses.sort((a, b) => a.delay - b.delay);
    
    // Queue the responses with cumulative delays
    let cumulativeDelay = 0;
    for (const response of characterResponses) {
      cumulativeDelay += response.delay;
      
      const queuedMessage = await MessageQueueService.enqueueMessage({
        conversation_id: conversationId,
        character: response.character,
        text: response.text,
        delay_ms: cumulativeDelay
      });
      
      if (queuedMessage) {
        logger.info(`Queued ${response.character} response (delay: ${cumulativeDelay}ms): ${response.text.substring(0, 50)}...`);
      }
    }
    
    // Process follow-up decisions for characters who didn't respond initially
    await CharacterDecisionService.processFollowUpDecisions(userMessage, conversationId, themeAnalysis, characterResponses);
    
    return results;
  }
  
  /**
   * Process follow-up decisions for characters to respond to other characters
   */
  private static async processFollowUpDecisions(
    userMessage: string,
    conversationId: number,
    themeAnalysis: ThemeAnalysisResult,
    initialResponses: Array<{character: string, text: string, delay: number}>
  ): Promise<void> {
    
    if (initialResponses.length === 0) return;
    
    const allCharacters = ['Fora', 'Jan', 'Lou'];
    const respondingCharacters = initialResponses.map(r => r.character);
    const nonRespondingCharacters = allCharacters.filter(c => !respondingCharacters.includes(c));
    
    // Give non-responding characters a chance to respond to the initial responses
    for (const character of nonRespondingCharacters) {
      const decision = await CharacterDecisionService.getCharacterDecision({
        character,
        userMessage,
        conversationId,
        themeAnalysis,
        previousCharacterMessages: initialResponses,
        isFollowUp: true
      });
      
      if (decision.shouldRespond && decision.response) {
        // Calculate delay after the last initial response
        const maxInitialDelay = Math.max(...initialResponses.map(r => r.delay));
        const followUpDelay = maxInitialDelay + decision.response.delay;
        
        const queuedMessage = await MessageQueueService.enqueueMessage({
          conversation_id: conversationId,
          character,
          text: decision.response.text,
          delay_ms: followUpDelay
        });
        
        if (queuedMessage) {
          logger.info(`Queued ${character} follow-up response (delay: ${followUpDelay}ms): ${decision.response.text.substring(0, 50)}...`);
        }
      }
    }
  }
  
  /**
   * Build the decision prompt for a character
   */
  private static buildDecisionPrompt(input: CharacterDecisionInput): string {
    const { userMessage, themeAnalysis, previousCharacterMessages, isFollowUp } = input;

    let prompt = `User message: "${userMessage}"\n`;
    prompt += `Conversation theme: ${themeAnalysis.theme}\n`;
    prompt += `Relevant skills: ${themeAnalysis.skills.join(', ') || 'none'}\n`;
    prompt += `Relevant specialists: ${themeAnalysis.specialists.join(', ') || 'none'}\n`;

    if (themeAnalysis.contextualInfo?.conversationHistory) {
      prompt += `\nConversation history:\n${themeAnalysis.contextualInfo.conversationHistory}\n`;
    }

    if (previousCharacterMessages && previousCharacterMessages.length > 0) {
      prompt += `\nOther characters' responses:\n`;
      previousCharacterMessages.forEach(msg => {
        prompt += `${msg.character}: ${msg.text}\n`;
      });
    }
    
    if (isFollowUp) {
      prompt += `\nThis is a follow-up opportunity - you can choose to respond to what other characters said or add your perspective.`;
    }
    
    return prompt;
  }

  /**
   * Format specialist context for injection into character system prompts
   */
  private static formatSpecialistContext(specialists: string[]): string {
    if (specialists.length === 0) {
      return '';
    }

    const specialistList = specialists.join(', ');
    return `**CURRENT CONVERSATION SPECIALISTS:**
The following specialists have been identified as relevant to this conversation: ${specialistList}
When appropriate, refer to these specialists as your friends who can help with their specific expertise.`;
  }

  /**
   * Calculate default delay based on theme and context
   */
  private static calculateDefaultDelay(themeAnalysis: ThemeAnalysisResult, isFollowUp?: boolean): number {
    let baseDelay = 3000; // 3 seconds default
    
    // Shorter delays for greetings
    if (themeAnalysis.isGeneralGreeting) {
      baseDelay = 2000;
    }
    
    // Longer delays for complex topics
    if (themeAnalysis.skills.length > 2) {
      baseDelay = 5000;
    }
    
    // Follow-up responses can be quicker
    if (isFollowUp) {
      baseDelay = Math.max(2000, baseDelay - 1000);
    }
    
    // Add some randomness
    return baseDelay + Math.floor(Math.random() * 2000);
  }
}
