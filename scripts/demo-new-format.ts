#!/usr/bin/env ts-node

/**
 * De<PERSON> script to show the improved test output format
 * Uses existing test data to demonstrate the new user experience timeline
 */

import * as fs from 'fs';
import * as path from 'path';

// Sample data that mimics what we get from the des-senior test
const sampleTestResult = {
  id: "prompt_1",
  prompt: "there's this one senior person on my team",
  success: true,
  response: {
    conversationId: 294,
    theme: "workplace dynamics",
    skills: ["professionalism", "awareness"],
    reply: [
      {
        character: "Fora",
        text: "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄",
        delay: 2000
      },
      {
        character: "Jan", 
        text: "Okay, so there's a senior person on your team. What's the vibe with them?",
        delay: 3000
      },
      {
        character: "<PERSON>",
        text: "Oh, dealing with senior folks can be a whole vibe. What's up with them?",
        delay: 3000
      }
    ]
  },
  duration: 4232,
  timestamp: "2025-07-07T15:35:34.503Z",
  conversationId: 294,
  messageCount: 6,
  delayedMessages: [
    {
      id: 669,
      character: "Fora",
      text: "Ugh, a difficult senior person? Tell me more. What are they doing that's making things rough? 🙄",
      delay_ms: 2000,
      source: "queued",
      scheduled_at: "2025-07-07T15:35:40.669Z"
    },
    {
      id: 670,
      character: "Jan",
      text: "Okay, so there's a senior person on your team. What's the vibe with them?",
      delay_ms: 5000,
      source: "queued", 
      scheduled_at: "2025-07-07T15:35:43.669Z"
    },
    {
      id: 671,
      character: "Lou",
      text: "Oh, dealing with senior folks can be a whole vibe. What's up with them?",
      delay_ms: 8000,
      source: "queued",
      scheduled_at: "2025-07-07T15:35:46.669Z"
    }
  ]
};

// Simulate the new format generation
function generateUserExperienceTimeline(characterReplies: any[]): any {
  // Filter to only actual messages that would be delivered to users
  const actualMessages = characterReplies.filter(reply => reply.source !== 'immediate');
  
  // Sort by delay to show chronological order
  const sortedMessages = actualMessages.sort((a, b) => a.delay - b.delay);
  
  const timeline = sortedMessages.map((reply, index) => ({
    order: index + 1,
    character: reply.character,
    text: reply.text,
    delayMs: reply.delay,
    delaySeconds: Math.round(reply.delay / 1000 * 10) / 10,
    timestamp: reply.scheduledAt || reply.timestamp
  }));

  return {
    totalMessages: timeline.length,
    characters: [...new Set(timeline.map(msg => msg.character))],
    timeline,
    totalDuration: timeline.length > 0 ? Math.max(...timeline.map(msg => msg.delayMs)) : 0
  };
}

function analyzeCharacterReplies(response: any, delayedMessages: any[]): any[] {
  const characterReplies: any[] = [];

  // Process immediate replies from the initial response (server response structure)
  if (response.reply && Array.isArray(response.reply)) {
    response.reply.forEach((reply: any) => {
      characterReplies.push({
        character: reply.character,
        text: reply.text,
        delay: reply.delay || 0,
        source: 'immediate',
        timestamp: new Date().toISOString()
      });
    });
  }

  // Process delayed messages (actual queued messages that will be delivered)
  delayedMessages.forEach((msg: any) => {
    characterReplies.push({
      character: msg.character,
      text: msg.text,
      delay: msg.delay_ms || msg.delay || 0,
      messageId: msg.id,
      source: msg.source || 'delayed',
      timestamp: msg.created_at || msg.scheduled_at || new Date().toISOString(),
      scheduledAt: msg.scheduled_at,
      originalDelay: msg.delay_ms
    });
  });

  return characterReplies;
}

// Generate the demo output
console.log('🎭 ForaChat Integration Test - New Format Demo');
console.log('='.repeat(50));

const characterReplies = analyzeCharacterReplies(sampleTestResult.response, sampleTestResult.delayedMessages);
const userExperience = generateUserExperienceTimeline(characterReplies);

console.log('\n### OLD FORMAT (Confusing):');
console.log('#### Character Replies');
characterReplies.forEach((reply, index) => {
  const truncatedText = reply.text.length > 80 ? reply.text.substring(0, 80) + '...' : reply.text;
  console.log(`${index + 1}. **${reply.character}** (${reply.source}, ${reply.delay}ms delay): "${truncatedText}"`);
});

console.log('\n### NEW FORMAT (Clear):');
console.log('#### 🎭 User Experience Simulation');
console.log('**What the user would see:**');
console.log(`- **Total Messages**: ${userExperience.totalMessages}`);
console.log(`- **Characters Responding**: ${userExperience.characters.join(', ')}`);
console.log(`- **Total Duration**: ${(userExperience.totalDuration / 1000).toFixed(1)}s`);
console.log('\n**Message Timeline:**');

if (userExperience.timeline.length > 0) {
  userExperience.timeline.forEach((msg: any) => {
    const truncatedText = msg.text.length > 80 ? msg.text.substring(0, 80) + '...' : msg.text;
    console.log(`${msg.order}. **${msg.character}** (after ${msg.delaySeconds}s): "${truncatedText}"`);
  });
} else {
  console.log('*No messages would be delivered to the user*');
}

console.log('\n#### 🔧 Technical Analysis');
const immediateReplies = characterReplies.filter(r => r.source === 'immediate').length;
const delayedReplies = characterReplies.filter(r => r.source !== 'immediate').length;
console.log(`- **Server Response Structure**: ${immediateReplies} immediate replies`);
console.log(`- **Queued Messages**: ${delayedReplies} delayed messages`);
console.log(`- **Processing Time**: ${(sampleTestResult.duration / 1000).toFixed(2)}s`);

console.log('\n🎯 Key Improvements:');
console.log('✅ Clear separation between user experience and technical details');
console.log('✅ Chronological timeline showing actual message delivery');
console.log('✅ Eliminates confusion about duplicate messages');
console.log('✅ Shows what users would actually see and when');
