
# Integration Test Report
Generated: 2025-07-07T16:55:22.384Z
Session ID: 412241b3-0724-4e1a-91fe-37cfad6af5ad

## Summary
- Total Tests: 6
- Successful: 6
- Failed: 0
- Success Rate: 100.0%
- Total Duration: 30.67s
- Average Duration: 5.11s

## Test Results

### prompt_1 ✅
- **Prompt**: "there's this one senior person on my team"
- **Duration**: 4.04s
- **Timestamp**: 2025-07-07T16:54:41.714Z
- **Conversation ID**: 305
- **Message Count**: 6
- **Theme**: workplace dynamics
- **Skills**: team dynamics, workplace behavior

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 3
- **Characters Responding**: <PERSON><PERSON>, <PERSON>, <PERSON>
- **Total Duration**: 9.5s

**Message Timeline:**
1. **Fora** (after 3s): "Ooh, a senior person on the team? That can be a whole vibe, sometimes good, some..."
2. **Jan** (after 6s): "Alright, lay it on me. What's the sitch with this senior person?"
3. **Lou** (after 9.5s): "Ooh, a senior person. Those dynamics can be tricky to read sometimes. What's up?"

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 3 delayed messages
- **Average Planned Delay**: 3167ms
- **Processing Time**: 4.04s
- **Character Decisions**: Fora (2), Jan (2), <PERSON> (2)

### prompt_2 ✅
- **Prompt**: "and they always talk to me like i'm a child"
- **Duration**: 5.33s
- **Timestamp**: 2025-07-07T16:54:47.752Z
- **Conversation ID**: 305
- **Message Count**: 9
- **Theme**: conflict resolution
- **Skills**: Conflict Resolution, Assertiveness, Verbal Communication, Professional Boundaries, Emotional Intelligence, Workplace Behavior

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 6
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 12.0s

**Message Timeline:**
1. **Fora** (after 3s): "Ooh, a senior person on the team? That can be a whole vibe, sometimes good, some..."
2. **Fora** (after 4s): "Ugh, that's the worst! Feeling patronized is never a good vibe. Des is super goo..."
3. **Jan** (after 6s): "Alright, lay it on me. What's the sitch with this senior person?"
4. **Jan** (after 8s): "Not to be harsh, but you gotta call that out. When they talk down to you, try a ..."
5. **Lou** (after 9.5s): "Ooh, a senior person. Those dynamics can be tricky to read sometimes. What's up?"
6. **Lou** (after 12s): "Ugh, that's a tough one. Being talked down to is super frustrating and totally i..."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 6 delayed messages
- **Average Planned Delay**: 4000ms
- **Processing Time**: 5.33s
- **Character Decisions**: Fora (3), Jan (3), Lou (3)

### prompt_3 ✅
- **Prompt**: "they'll be like "oh sweetie, that's not how we do things here""
- **Duration**: 3.84s
- **Timestamp**: 2025-07-07T16:54:55.081Z
- **Conversation ID**: 305
- **Message Count**: 12
- **Theme**: conflict resolution
- **Skills**: Conflict Resolution, Assertiveness, Verbal Communication, Professional Boundaries, Emotional Intelligence, Workplace Behavior, Understanding Your Boss's Needs

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 9
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 12.0s

**Message Timeline:**
1. **Fora** (after 3s): "Oof, being talked to like a child by a senior person? That's definitely not the ..."
2. **Fora** (after 4s): "Ugh, that's the worst! Feeling patronized is never a good vibe. Des is super goo..."
3. **Fora** (after 4s): "Ugh, that patronizing vibe is the worst. It's like they think they're the only o..."
4. **Jan** (after 6s): "Oof, that's rough. Dealing with condescending senior folks is a whole vibe. What..."
5. **Jan** (after 8s): "Not to be harsh, but you gotta call that out. When they talk down to you, try a ..."
6. **Jan** (after 8s): "Not to be harsh, but you need to shut that down, politely but firmly. Next time ..."
7. **Lou** (after 9.5s): "Ooh, a senior person. Those dynamics can be tricky to read sometimes. What's up?"
8. **Lou** (after 12s): "Ugh, that's a tough one. Being talked down to is super frustrating and totally i..."
9. **Lou** (after 12s): "Ugh, that condescending tone is so frustrating. It's like they're trying to asse..."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 9 delayed messages
- **Average Planned Delay**: 4000ms
- **Processing Time**: 3.84s
- **Character Decisions**: Fora (4), Jan (4), Lou (4)

### prompt_4 ✅
- **Prompt**: "it's so patronizing"
- **Duration**: 4.37s
- **Timestamp**: 2025-07-07T16:55:00.922Z
- **Conversation ID**: 305
- **Message Count**: 13
- **Theme**: conflict resolution
- **Skills**: Conflict Resolution, Assertiveness, Verbal Communication, Professional Boundaries, Emotional Intelligence, Workplace Behavior

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 10
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 13.0s

**Message Timeline:**
1. **Fora** (after 4s): "Ugh, that's the worst! Feeling patronized is never a good vibe. Des is super goo..."
2. **Fora** (after 4s): "Ugh, that patronizing vibe is the worst. It's like they think they're the only o..."
3. **Fora** (after 4s): "Yeah, that patronizing energy is super draining. It's like, we're all adults her..."
4. **Jan** (after 8s): "Not to be harsh, but you gotta call that out. When they talk down to you, try a ..."
5. **Jan** (after 8s): "Not to be harsh, but you need to shut that down, politely but firmly. Next time ..."
6. **Jan** (after 8s): "Not to be harsh, but that "sweetie" comment is a big red flag. Directness is key..."
7. **Lou** (after 9.5s): "Ooh, dealing with someone who talks down to you is rough. That 'oh sweetie' vibe..."
8. **Lou** (after 12s): "Ugh, that's a tough one. Being talked down to is super frustrating and totally i..."
9. **Lou** (after 12s): "Ugh, that condescending tone is so frustrating. It's like they're trying to asse..."
10. **Lou** (after 13s): "It's rough when someone's tone feels like they're looking down on you. It makes ..."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 10 delayed messages
- **Average Planned Delay**: 4333ms
- **Processing Time**: 4.37s
- **Character Decisions**: Fora (4), Jan (4), Lou (5)

### prompt_5 ✅
- **Prompt**: "how do i get them to respect me?"
- **Duration**: 7.41s
- **Timestamp**: 2025-07-07T16:55:07.291Z
- **Conversation ID**: 305
- **Message Count**: 14
- **Theme**: conflict resolution
- **Skills**: Conflict Resolution, Assertiveness, Verbal Communication, Professional Boundaries, Emotional Intelligence, Influence and Persuasion

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 11
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 13.0s

**Message Timeline:**
1. **Fora** (after 3s): "Hey, that patronizing talk is a real drag. Wanting respect is totally valid. Des..."
2. **Fora** (after 4s): "Totally feel you on that patronizing vibe. It's tough when someone talks down to..."
3. **Fora** (after 4s): "Yeah, that patronizing energy is super draining. It's like, we're all adults her..."
4. **Jan** (after 7s): "Real talk though, the 'oh sweetie' from a senior person is wild. You need to set..."
5. **Jan** (after 8s): "Not to be harsh, but you gotta call that out. When they talk down to you, try a ..."
6. **Jan** (after 8s): "Not to be harsh, but you need to shut that down, politely but firmly. Next time ..."
7. **Jan** (after 8s): "Not to be harsh, but that "sweetie" comment is a big red flag. Directness is key..."
8. **Lou** (after 11s): "Gaining respect can be tricky. Sometimes, it's less about what they say and more..."
9. **Lou** (after 12s): "Fora and Jan are spot on, that patronizing tone is rough. It sounds like it's re..."
10. **Lou** (after 12s): "Ugh, that condescending tone is so frustrating. It's like they're trying to asse..."
11. **Lou** (after 13s): "It's rough when someone's tone feels like they're looking down on you. It makes ..."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 11 delayed messages
- **Average Planned Delay**: 3667ms
- **Processing Time**: 7.41s
- **Character Decisions**: Fora (4), Jan (5), Lou (5)

### prompt_6 ✅
- **Prompt**: "i'm not a baby, i have a degree"
- **Duration**: 5.68s
- **Timestamp**: 2025-07-07T16:55:16.703Z
- **Conversation ID**: 305
- **Message Count**: 14
- **Theme**: conflict resolution
- **Skills**: Assertiveness, Verbal Communication, Professional Boundaries, Emotional Intelligence, Clarification, Professionalism

#### 🎭 User Experience Simulation
**What the user would see:**
- **Total Messages**: 11
- **Characters Responding**: Fora, Jan, Lou
- **Total Duration**: 13.0s

**Message Timeline:**
1. **Fora** (after 3s): "Hey, that patronizing talk is a real drag. Wanting respect is totally valid. Des..."
2. **Fora** (after 3s): "It's a whole vibe when you're trying to get folks to respect you and they're com..."
3. **Fora** (after 4s): "Totally get wanting that respect! Building that confidence is key. Since you're ..."
4. **Jan** (after 7s): "Real talk though, the 'oh sweetie' from a senior person is wild. You need to set..."
5. **Lou** (after 7s): "It's rough when you're not getting the respect you deserve. Sometimes people def..."
6. **Jan** (after 8s): "Gaining respect is key. Beyond a specific comeback, focus on consistently demons..."
7. **Jan** (after 8s): "Not to be harsh, but that "sweetie" comment is a big red flag. Directness is key..."
8. **Lou** (after 11s): "Gaining respect can be tricky. Sometimes, it's less about what they say and more..."
9. **Jan** (after 11.5s): "Real talk, your degree means you're qualified. When they hit you with the 'sweet..."
10. **Lou** (after 12s): "Yeah, that patronizing 'sweetie' is a major ick. It sounds like you're really tr..."
11. **Lou** (after 13s): "It's rough when someone's tone feels like they're looking down on you. It makes ..."

#### 🔧 Technical Analysis
- **Server Response Structure**: 3 immediate replies
- **Queued Messages**: 11 delayed messages
- **Average Planned Delay**: 3833ms
- **Processing Time**: 5.68s
- **Character Decisions**: Fora (4), Jan (5), Lou (5)
